defmodule MqttableWeb.SendMessageModalComponent do
  @moduledoc """
  A reusable modal component for sending MQTT messages.

  This component provides a form for composing and sending MQTT messages with support for:
  - Client selection with automatic fallback
  - Form state persistence across modal open/close cycles
  - MQTT 5.0 properties and user properties
  - Click-outside-to-close functionality
  """

  use MqttableWeb, :live_component

  require Logger

  @impl true
  def mount(socket) do
    socket =
      socket
      |> assign_new(:show_modal, fn -> false end)
      |> assign_new(:publish_form, fn -> default_publish_form() end)

    {:ok, socket}
  end

  @impl true
  def update(assigns, socket) do
    Logger.debug(
      "SendMessageModalComponent update called with assigns keys: #{inspect(Map.keys(assigns))}"
    )

    # Store the broker name when modal is shown, so we can use it when closing
    current_broker_name =
      if assigns[:show_modal] && assigns[:active_broker_name] do
        assigns[:active_broker_name]
      else
        socket.assigns[:stored_broker_name]
      end

    # Use form state from parent if provided, otherwise use current or default
    current_form =
      assigns[:form_state] ||
        socket.assigns[:publish_form] ||
        default_publish_form()

    Logger.debug("Current form state: #{inspect(current_form)}")

    # Smart client selection logic
    updated_form =
      if assigns[:show_modal] do
        smart_client_selection(current_form, assigns[:active_broker_name])
      else
        current_form
      end

    Logger.debug("After smart client selection: #{inspect(updated_form)}")

    # If smart client selection changed the client_id, save the updated form state
    if updated_form != current_form && assigns[:show_modal] do
      Logger.debug("Smart client selection changed form, saving updated state")
      cleaned_form = clean_form_for_storage(updated_form)
      send(self(), {:update_send_modal_form, cleaned_form, current_broker_name})
    end

    # Load MQTT 5.0 properties collapse state from ui_state
    mqtt5_collapsed = get_mqtt5_properties_collapsed_state(assigns[:active_broker_name])

    socket =
      socket
      |> assign(assigns)
      |> assign(:publish_form, updated_form)
      |> assign(:mqtt5_properties_collapsed, mqtt5_collapsed)
      |> assign(:stored_broker_name, current_broker_name)

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <!-- Send Message Modal -->
      <dialog id="send-message-modal" class={"modal #{if @show_modal, do: "modal-open", else: ""}"}>
        <div class="modal-backdrop" phx-click="close_modal" phx-target={@myself}></div>
        <div class="modal-box max-w-md ml-auto mr-6 mt-6 mb-6 h-fit max-h-[calc(100vh-3rem)] send-message-modal">
          <!-- Modal Header -->
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold flex items-center">
              <.icon name="hero-paper-airplane" class="size-5 mr-2" /> Send MQTT Message
            </h3>
            <button
              class="btn btn-sm btn-circle btn-ghost"
              phx-click="close_modal"
              phx-target={@myself}
            >
              ✕
            </button>
          </div>
          
    <!-- Modal Content -->
          <div class="space-y-4 overflow-y-auto max-h-[calc(100vh-8rem)]">
            <.form
              for={%{}}
              as={:publish}
              phx-submit="send_message"
              phx-change="form_changed"
              phx-target={@myself}
              class="space-y-4"
            >
              <!-- Client ID Selection -->
              <div class="form-control w-full">
                <label class="label">
                  <span class="label-text font-medium">
                    Client <span class="text-error">*</span>
                  </span>
                </label>
                <!-- Debug info -->
                <div class="text-xs text-gray-500 mb-1">
                  Debug: Current client_id = "{@publish_form["client_id"]}"
                </div>
                <select
                  name="client_id"
                  class="select select-bordered w-full"
                  required
                  phx-change="client_selection_changed"
                  phx-target={@myself}
                >
                  <option value="" selected={@publish_form["client_id"] == ""}>
                    Select a connected client
                  </option>
                  <%= for client <- get_connected_clients(@active_broker_name || "") do %>
                    <option
                      value={client.client_id}
                      selected={@publish_form["client_id"] == client.client_id}
                    >
                      {client.client_id} (MQTT {client.mqtt_version || "5.0"})
                    </option>
                  <% end %>
                </select>
              </div>
              
    <!-- Topic -->
              <div class="form-control w-full">
                <label class="label">
                  <span class="label-text font-medium">
                    Topic <span class="text-error">*</span>
                  </span>
                </label>
                <input
                  type="text"
                  name="topic"
                  value={@publish_form["topic"]}
                  placeholder="Enter topic (e.g., 'device/sensor/temperature')"
                  class="input input-bordered w-full"
                  required
                />
              </div>
              
    <!-- Payload -->
              <div class="form-control w-full">
                <label class="label">
                  <span class="label-text font-medium">Payload</span>
                </label>
                <textarea
                  name="payload"
                  placeholder="Enter message payload"
                  class="textarea textarea-bordered w-full h-32"
                ><%= @publish_form["payload"] %></textarea>
              </div>
              
    <!-- QoS and Retain -->
              <div class="grid grid-cols-2 gap-4">
                <div class="form-control w-full">
                  <label class="label">
                    <span class="label-text font-medium">QoS Level</span>
                  </label>
                  <select name="qos" class="select select-bordered w-full">
                    <option value="0" selected={@publish_form["qos"] == 0}>0 - At most once</option>
                    <option value="1" selected={@publish_form["qos"] == 1}>1 - At least once</option>
                    <option value="2" selected={@publish_form["qos"] == 2}>2 - Exactly once</option>
                  </select>
                </div>

                <div class="form-control w-full">
                  <label class="label">
                    <span class="label-text font-medium">Retain Message</span>
                  </label>
                  <div class="flex items-center gap-2 mt-2">
                    <input
                      type="checkbox"
                      name="retain"
                      checked={@publish_form["retain"]}
                      class="toggle toggle-primary"
                    />
                    <span class="label-text">Retain</span>
                  </div>
                </div>
              </div>
              
    <!-- MQTT 5.0 Properties Section -->
              <%= if show_mqtt5_properties?(@publish_form["client_id"], @active_broker_name) do %>
                <div class="collapse collapse-arrow bg-base-200 rounded-box">
                  <input
                    type="checkbox"
                    checked={!@mqtt5_properties_collapsed}
                    phx-click="toggle_mqtt5_properties"
                    phx-target={@myself}
                  />
                  <div class="collapse-title text-lg font-medium">
                    MQTT 5.0 Properties
                  </div>
                  <div class="collapse-content">
                    
    <!-- Content Type and Payload Format Indicator -->
                    <div class="grid grid-cols-2 gap-4">
                      <div class="form-control w-full">
                        <label class="label">
                          <span class="label-text font-medium">Content Type</span>
                        </label>
                        <input
                          type="text"
                          name="content_type"
                          value={@publish_form["content_type"]}
                          placeholder="e.g., application/json"
                          class="input input-bordered w-full"
                        />
                      </div>

                      <div class="form-control w-full">
                        <label class="label">
                          <span class="label-text font-medium">Payload Format</span>
                        </label>
                        <div class="flex items-center gap-2 mt-2">
                          <input
                            type="checkbox"
                            name="payload_format_indicator"
                            checked={@publish_form["payload_format_indicator"]}
                            class="toggle toggle-primary"
                          />
                          <span class="label-text">UTF-8 String</span>
                        </div>
                      </div>
                    </div>
                    
    <!-- Message Expiry and Topic Alias -->
                    <div class="grid grid-cols-2 gap-4">
                      <div class="form-control w-full">
                        <label class="label">
                          <span class="label-text font-medium">Message Expiry (seconds)</span>
                        </label>
                        <input
                          type="number"
                          name="message_expiry_interval"
                          value={
                            if @publish_form["message_expiry_interval"] == 0,
                              do: "",
                              else: @publish_form["message_expiry_interval"]
                          }
                          placeholder="0 (no expiry)"
                          min="0"
                          class="input input-bordered w-full"
                        />
                      </div>

                      <div class="form-control w-full">
                        <label class="label">
                          <span class="label-text font-medium">Topic Alias</span>
                        </label>
                        <input
                          type="number"
                          name="topic_alias"
                          value={
                            if @publish_form["topic_alias"] == 0,
                              do: "",
                              else: @publish_form["topic_alias"]
                          }
                          placeholder="0 (no alias)"
                          min="0"
                          max="65535"
                          class="input input-bordered w-full"
                        />
                      </div>
                    </div>
                    
    <!-- Response Topic and Correlation Data -->
                    <div class="grid grid-cols-2 gap-4">
                      <div class="form-control w-full">
                        <label class="label">
                          <span class="label-text font-medium">Response Topic</span>
                        </label>
                        <input
                          type="text"
                          name="response_topic"
                          value={@publish_form["response_topic"]}
                          placeholder="Topic for response"
                          class="input input-bordered w-full"
                        />
                      </div>

                      <div class="form-control w-full">
                        <label class="label">
                          <span class="label-text font-medium">Correlation Data</span>
                        </label>
                        <input
                          type="text"
                          name="correlation_data"
                          value={@publish_form["correlation_data"]}
                          placeholder="Correlation identifier"
                          class="input input-bordered w-full"
                        />
                      </div>
                    </div>
                    
    <!-- User Properties -->
                    <div class="form-control w-full">
                      <label class="label">
                        <span class="label-text font-medium">User Properties</span>
                      </label>
                      <div class="space-y-2">
                        <%= for {property, index} <- Enum.with_index(@publish_form["user_properties"] || []) do %>
                          <div class="flex gap-2 items-center">
                            <input
                              type="text"
                              name={"user_property_key_#{index}"}
                              value={property["key"]}
                              placeholder="Property key"
                              class="input input-bordered flex-1"
                              phx-change="user_property_changed"
                              phx-target={@myself}
                            />
                            <input
                              type="text"
                              name={"user_property_value_#{index}"}
                              value={property["value"]}
                              placeholder="Property value"
                              class="input input-bordered flex-1"
                              phx-change="user_property_changed"
                              phx-target={@myself}
                            />
                            <div class="flex-shrink-0">
                              <%= if index == length(@publish_form["user_properties"]) - 1 do %>
                                <button
                                  type="button"
                                  class="btn btn-sm btn-primary"
                                  phx-click="add_user_property"
                                  phx-target={@myself}
                                >
                                  <.icon name="hero-plus" class="size-4" />
                                </button>
                              <% else %>
                                <button
                                  type="button"
                                  class="btn btn-sm btn-error"
                                  phx-click="remove_user_property"
                                  phx-value-index={index}
                                  phx-target={@myself}
                                >
                                  <.icon name="hero-trash" class="size-4" />
                                </button>
                              <% end %>
                            </div>
                          </div>
                        <% end %>
                        <!-- Add User Property button when list is empty -->
                        <%= if length(@publish_form["user_properties"] || []) == 0 do %>
                          <div class="flex justify-center">
                            <button
                              type="button"
                              class="btn btn-sm btn-primary"
                              phx-click="add_user_property"
                              phx-target={@myself}
                            >
                              <.icon name="hero-plus" class="size-4 mr-2" /> Add User Property
                            </button>
                          </div>
                        <% end %>
                      </div>
                    </div>
                  </div>
                </div>
              <% end %>
              
    <!-- Submit Button -->
              <div class="form-control w-full">
                <button type="submit" class="btn btn-primary">
                  <.icon name="hero-paper-airplane" class="size-4 mr-2" /> Send Message
                </button>
              </div>
            </.form>
          </div>
        </div>
      </dialog>
    </div>
    """
  end

  # Event Handlers

  @impl true
  def handle_event("close_modal", _params, socket) do
    # Clean and save form state when closing modal
    cleaned_form = clean_form_for_storage(socket.assigns.publish_form)
    broker_name = socket.assigns[:stored_broker_name]

    Logger.debug(
      "Closing modal, saving form state: #{inspect(cleaned_form)} for broker: #{broker_name}"
    )

    send(self(), {:update_send_modal_form, cleaned_form, broker_name})
    # Send close event to the parent LiveView
    send(self(), {:close_send_modal})
    {:noreply, socket}
  end

  @impl true
  def handle_event("form_changed", params, socket) do
    # Update form state with all current values from the form
    updated_form = update_form_with_params(socket.assigns.publish_form, params)
    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("client_selection_changed", %{"client_id" => client_id}, socket) do
    updated_form = Map.put(socket.assigns.publish_form, "client_id", client_id)
    # Clean and notify parent component about form state change
    cleaned_form = clean_form_for_storage(updated_form)
    broker_name = socket.assigns[:stored_broker_name]
    send(self(), {:update_send_modal_form, cleaned_form, broker_name})
    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("user_property_changed", params, socket) do
    # Extract user properties from the form parameters
    current_properties = socket.assigns.publish_form["user_properties"] || []

    # Parse the user property fields from params
    updated_properties = parse_user_properties_from_params(params, current_properties)

    # Preserve all existing form fields, only update user_properties
    updated_form = Map.put(socket.assigns.publish_form, "user_properties", updated_properties)

    # Clean and notify parent component about form state change
    cleaned_form = clean_form_for_storage(updated_form)
    broker_name = socket.assigns[:stored_broker_name]
    send(self(), {:update_send_modal_form, cleaned_form, broker_name})
    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("add_user_property", _params, socket) do
    current_properties = socket.assigns.publish_form["user_properties"] || []
    new_properties = current_properties ++ [%{"key" => "", "value" => ""}]

    # Preserve all existing form fields, only update user_properties
    updated_form = Map.put(socket.assigns.publish_form, "user_properties", new_properties)

    # Clean and notify parent component about form state change
    cleaned_form = clean_form_for_storage(updated_form)
    broker_name = socket.assigns[:stored_broker_name]
    send(self(), {:update_send_modal_form, cleaned_form, broker_name})
    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("remove_user_property", %{"index" => index_str}, socket) do
    index = String.to_integer(index_str)
    current_properties = socket.assigns.publish_form["user_properties"] || []

    new_properties = List.delete_at(current_properties, index)

    # Preserve all existing form fields, only update user_properties
    updated_form = Map.put(socket.assigns.publish_form, "user_properties", new_properties)

    # Clean and notify parent component about form state change
    cleaned_form = clean_form_for_storage(updated_form)
    broker_name = socket.assigns[:stored_broker_name]
    send(self(), {:update_send_modal_form, cleaned_form, broker_name})
    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("toggle_mqtt5_properties", _params, socket) do
    broker_name = socket.assigns[:active_broker_name]
    current_collapsed = socket.assigns[:mqtt5_properties_collapsed] || false
    new_collapsed = !current_collapsed

    # Save the collapse state to ui_state
    if broker_name do
      save_mqtt5_properties_collapsed_state(broker_name, new_collapsed)
    end

    {:noreply, assign(socket, :mqtt5_properties_collapsed, new_collapsed)}
  end

  @impl true
  def handle_event("send_message", params, socket) do
    # Extract form parameters
    client_id = params["client_id"]
    topic = params["topic"]
    payload = params["payload"] || ""
    qos = String.to_integer(params["qos"] || "0")
    retain = params["retain"] == "on"

    # Update form state with all current values (including MQTT 5.0 properties)
    updated_form = update_form_with_params(socket.assigns.publish_form, params)

    socket = assign(socket, :publish_form, updated_form)

    # Validate required fields
    if client_id != "" && topic != "" do
      # Build MQTT 5.0 properties if the client supports it
      connected_clients = get_connected_clients(socket.assigns[:active_broker_name] || "")
      properties = build_mqtt5_publish_properties(params, connected_clients, client_id)

      # Prepare publish options
      publish_opts = [qos: qos, retain: retain]

      # Add properties if any
      publish_opts =
        if map_size(properties) > 0 do
          [{:properties, properties} | publish_opts]
        else
          publish_opts
        end

      # Attempt to publish the message
      case Mqttable.MqttClient.Manager.publish(client_id, topic, payload, publish_opts) do
        {:ok, packet_id} ->
          # Success - clean and save form state and show success message but keep modal open
          cleaned_form = clean_form_for_storage(updated_form)
          broker_name = socket.assigns[:stored_broker_name]
          send(self(), {:update_send_modal_form, cleaned_form, broker_name})
          send(self(), {:message_sent_successfully, packet_id})
          {:noreply, socket}

        {:error, :not_connected} ->
          send(self(), {:message_send_error, "Client is not connected"})
          {:noreply, socket}

        {:error, _reason, error_message} ->
          send(self(), {:message_send_error, "Failed to send message: #{error_message}"})
          {:noreply, socket}
      end
    else
      # Validation failed
      send(self(), {:message_send_error, "Please fill in all required fields"})
      {:noreply, socket}
    end
  end

  # Helper Functions

  defp clean_form_for_storage(form_state) when is_map(form_state) do
    # Remove temporary form fields and Phoenix internal fields that shouldn't be persisted
    form_state
    |> Enum.reject(fn {key, _value} ->
      String.starts_with?(key, "user_property_key_") or
        String.starts_with?(key, "user_property_value_") or
        String.starts_with?(key, "_target") or
        String.starts_with?(key, "_unused_")
    end)
    |> Enum.into(%{})
  end

  defp default_publish_form do
    %{
      "client_id" => "",
      "topic" => "",
      "payload" => "",
      "qos" => 0,
      "retain" => false,
      # MQTT 5.0 properties - use proper data types
      "content_type" => "",
      "payload_format_indicator" => false,
      "message_expiry_interval" => 0,
      "topic_alias" => 0,
      "response_topic" => "",
      "correlation_data" => "",
      "user_properties" => []
    }
  end

  defp update_form_with_params(current_form, params) do
    # Update form with all parameters, handling type conversions
    updated_form =
      Enum.reduce(params, current_form, fn {key, value}, acc ->
        case key do
          "qos" ->
            Map.put(acc, key, String.to_integer(value))

          "retain" ->
            Map.put(acc, key, value == "on")

          "payload_format_indicator" ->
            Map.put(acc, key, value == "on")

          "message_expiry_interval" ->
            # Convert to integer, default to 0 if empty or invalid
            case value do
              "" ->
                Map.put(acc, key, 0)

              val when is_binary(val) ->
                case Integer.parse(val) do
                  {int_val, ""} when int_val >= 0 -> Map.put(acc, key, int_val)
                  _ -> Map.put(acc, key, 0)
                end

              val when is_integer(val) ->
                Map.put(acc, key, val)

              _ ->
                Map.put(acc, key, 0)
            end

          "topic_alias" ->
            # Convert to integer, default to 0 if empty or invalid
            case value do
              "" ->
                Map.put(acc, key, 0)

              val when is_binary(val) ->
                case Integer.parse(val) do
                  {int_val, ""} when int_val >= 0 and int_val <= 65535 ->
                    Map.put(acc, key, int_val)

                  _ ->
                    Map.put(acc, key, 0)
                end

              val when is_integer(val) ->
                Map.put(acc, key, val)

              _ ->
                Map.put(acc, key, 0)
            end

          _ ->
            Map.put(acc, key, value)
        end
      end)

    # Handle user properties separately if they exist in params
    user_properties = extract_user_properties_from_params(params)

    if length(user_properties) > 0 do
      Map.put(updated_form, "user_properties", user_properties)
    else
      updated_form
    end
  end

  defp parse_user_properties_from_params(params, current_properties) do
    # Extract user property fields from params and update current properties
    params
    |> Enum.filter(fn {key, _value} -> String.starts_with?(key, "user_property_") end)
    |> Enum.reduce(current_properties, fn {param_key, value}, acc ->
      case extract_index_and_field(param_key) do
        {index, field} when index < length(acc) ->
          List.update_at(acc, index, fn property ->
            Map.put(property, field, value)
          end)

        _ ->
          acc
      end
    end)
  end

  defp extract_index_and_field(param_key) do
    # Extract index and field from keys like "user_property_key_0" or "user_property_value_0"
    case String.split(param_key, "_") do
      ["user", "property", field, index_str] ->
        case Integer.parse(index_str) do
          {index, ""} -> {index, field}
          _ -> nil
        end

      _ ->
        nil
    end
  end

  defp extract_user_properties_from_params(params) do
    # Extract user properties from form params
    params
    |> Enum.filter(fn {key, _value} -> String.starts_with?(key, "user_property_") end)
    |> Enum.group_by(fn {key, _value} ->
      # Extract index from key like "user_property_key_0" or "user_property_value_0"
      key
      |> String.split("_")
      |> List.last()
      |> String.to_integer()
    end)
    |> Enum.sort_by(fn {index, _} -> index end)
    |> Enum.map(fn {_index, properties} ->
      # Convert list of key-value pairs to a map
      Enum.reduce(properties, %{"key" => "", "value" => ""}, fn {param_key, value}, acc ->
        if String.contains?(param_key, "_key_") do
          Map.put(acc, "key", value)
        else
          Map.put(acc, "value", value)
        end
      end)
    end)
  end

  defp show_mqtt5_properties?(client_id, active_broker_name) do
    # Show MQTT 5.0 properties if client supports MQTT 5.0
    if client_id != "" do
      connected_clients = get_connected_clients(active_broker_name || "")

      case Enum.find(connected_clients, fn client -> client.client_id == client_id end) do
        %{mqtt_version: version} when version in ["5.0", "5"] -> true
        _ -> false
      end
    else
      false
    end
  end

  defp smart_client_selection(form, active_broker_name) do
    connected_clients = get_connected_clients(active_broker_name || "")
    current_client_id = form["client_id"]

    # Check if current client is still valid and connected
    client_still_connected =
      Enum.any?(connected_clients, fn client ->
        client.client_id == current_client_id
      end)

    # If no client selected or current client disconnected, select first available
    if current_client_id == "" || current_client_id == nil || !client_still_connected do
      case connected_clients do
        [first_client | _] ->
          Logger.debug(
            "Smart client selection: selecting #{first_client.client_id} (MQTT #{first_client.mqtt_version || "5.0"})"
          )

          Map.put(form, "client_id", first_client.client_id)

        [] ->
          Logger.debug("Smart client selection: no connected clients available")
          form
      end
    else
      Logger.debug("Smart client selection: keeping current client #{current_client_id}")
      form
    end
  end

  defp get_connected_clients(_broker_name) do
    Mqttable.MqttClient.Manager.get_connected_clients()
  end

  defp build_mqtt5_publish_properties(params, connected_clients, client_id) do
    # Check if client supports MQTT 5.0
    client = Enum.find(connected_clients, fn c -> c.client_id == client_id end)

    case client do
      %{mqtt_version: version} when version in ["5.0", "5"] ->
        # Build MQTT 5.0 properties map
        properties = %{}

        # Add content type if provided
        properties =
          if params["content_type"] && params["content_type"] != "" do
            Map.put(properties, :"Content-Type", params["content_type"])
          else
            properties
          end

        # Add payload format indicator
        properties =
          if params["payload_format_indicator"] == "on" do
            Map.put(properties, :"Payload-Format-Indicator", 1)
          else
            properties
          end

        # Add message expiry interval
        properties =
          if params["message_expiry_interval"] && params["message_expiry_interval"] != "" do
            case Integer.parse(params["message_expiry_interval"]) do
              {interval, ""} when interval >= 0 ->
                Map.put(properties, :"Message-Expiry-Interval", interval)

              _ ->
                properties
            end
          else
            properties
          end

        # Add topic alias
        properties =
          if params["topic_alias"] && params["topic_alias"] != "" do
            case Integer.parse(params["topic_alias"]) do
              {alias, ""} when alias >= 0 and alias <= 65535 ->
                # Only add topic alias if it's greater than 0 (0 means no alias)
                if alias > 0 do
                  Map.put(properties, :"Topic-Alias", alias)
                else
                  properties
                end

              _ ->
                properties
            end
          else
            properties
          end

        # Add response topic
        properties =
          if params["response_topic"] && params["response_topic"] != "" do
            Map.put(properties, :"Response-Topic", params["response_topic"])
          else
            properties
          end

        # Add correlation data
        properties =
          if params["correlation_data"] && params["correlation_data"] != "" do
            Map.put(properties, :"Correlation-Data", params["correlation_data"])
          else
            properties
          end

        # Add user properties
        user_properties = extract_user_properties_from_params(params)

        valid_user_properties =
          Enum.filter(user_properties, fn %{"key" => key, "value" => value} ->
            key != "" && value != ""
          end)

        properties =
          if length(valid_user_properties) > 0 do
            user_props_list =
              Enum.map(valid_user_properties, fn %{"key" => key, "value" => value} ->
                {key, value}
              end)

            Map.put(properties, :"User-Property", user_props_list)
          else
            properties
          end

        properties

      _ ->
        # Client doesn't support MQTT 5.0 or not found
        %{}
    end
  end

  defp get_mqtt5_properties_collapsed_state(broker_name) do
    if broker_name do
      ui_state = Mqttable.ConnectionSets.get_ui_state()
      mqtt5_collapsed_states = Map.get(ui_state, :mqtt5_properties_collapsed, %{})
      Map.get(mqtt5_collapsed_states, broker_name, false)
    else
      false
    end
  end

  defp save_mqtt5_properties_collapsed_state(broker_name, collapsed) do
    ui_state = Mqttable.ConnectionSets.get_ui_state()
    mqtt5_collapsed_states = Map.get(ui_state, :mqtt5_properties_collapsed, %{})
    updated_mqtt5_collapsed_states = Map.put(mqtt5_collapsed_states, broker_name, collapsed)

    updated_ui_state =
      Map.put(ui_state, :mqtt5_properties_collapsed, updated_mqtt5_collapsed_states)

    Mqttable.ConnectionSets.update_ui_state(updated_ui_state)
  end
end
